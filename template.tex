%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% Medium Length Professional CV
% LaTeX Template
% Version 3.0 (December 17, 2022)
%
% This template originates from:
% https://www.LaTeXTemplates.com
%
% Author:
% Vel (<EMAIL>)
%
% Original author:
% <PERSON> (http://www.treyhunner.com/)
%
% License:
% CC BY-NC-SA 4.0 (https://creativecommons.org/licenses/by-nc-sa/4.0/)
%
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

%----------------------------------------------------------------------------------------
%	PACKAGES AND OTHER DOCUMENT CONFIGURATIONS
%----------------------------------------------------------------------------------------

\documentclass[
	%a4paper, % Uncomment for A4 paper size (default is US letter)
	11pt, % Default font size, can use 10pt, 11pt or 12pt
]{resume} % Use the resume class

\usepackage{ebgaramond} % Use the EB Garamond font

%------------------------------------------------

\name{John Smith} % Your name to appear at the top

% You can use the \address command up to 3 times for 3 different addresses or pieces of contact information
% Any new lines (\\) you use in the \address commands will be converted to symbols, so each address will appear as a single line.

\address{123 Broadway \\ City, State 12345} % Main address

\address{123 Pleasant Lane \\ City, State 12345} % A secondary address (optional)

\address{(011)~$\cdot$~899~$\cdot$~9881 \\ <EMAIL>} % Contact information

%----------------------------------------------------------------------------------------

\begin{document}

%----------------------------------------------------------------------------------------
%	EDUCATION SECTION
%----------------------------------------------------------------------------------------

\begin{rSection}{Education}
	
	\textbf{University of California, Berkeley} \hfill \textit{June 2004} \\ 
	B.S. in Computer Science \& Engineering \\
	Minor in Linguistics \smallskip \\
	Member of Eta Kappa Nu \\
	Member of Upsilon Pi Epsilon \\
	Overall GPA: 5.678
	
\end{rSection}

%----------------------------------------------------------------------------------------
%	WORK EXPERIENCE SECTION
%----------------------------------------------------------------------------------------

\begin{rSection}{Experience}

	\begin{rSubsection}{ACME, Inc}{October 2010 - Present}{Web Developer}{Palo Alto, CA}
		\item Lorem ipsum dolor sit amet, consectetur adipiscing elit. Donec a diam lectus.
		\item Donec et mollis dolor. Praesent et diam eget libero Adobe Coldfusion egestas mattis sit amet vitae augue.
		\item Nam tincidunt congue enim, ut porta lorem Microsoft SQL lacinia consectetur.
		\item Donec ut libero sed arcu vehicula ultricies a non tortor. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
		\item Pellentesque auctor nisi id magna consequat JavaScript sagittis.
		\item Aliquam at massa ipsum. Quisque bash bibendum purus convallis nulla ultrices ultricies.
	\end{rSubsection}

%------------------------------------------------

	\begin{rSubsection}{AJAX Hosting}{December 2009 - October 2010}{Lead Developer}{Austin, TX}
		\item Aenean ut gravida lorem. Ut turpis felis, Perl pulvinar a semper sed, adipiscing id dolor.
		\item Curabitur dapibus enim sit amet elit pharetra tincidunt website feugiat nisl imperdiet. Ut convallis AJAX libero in urna ultrices accumsan.
		\item Cum sociis natoque penatibus et magnis dis MySQL parturient montes, nascetur ridiculus mus.
		\item In rutrum accumsan ultricies. Mauris vitae nisi at sem facilisis semper ac in est.
		\item Nullam cursus suscipit nisi, et ultrices justo sodales nec. Fusce venenatis facilisis lectus ac semper.
	\end{rSubsection}

%------------------------------------------------

	\begin{rSubsection}{TinySoft}{January 2008 - April 2010}{Web Designer \& Developer}{Gainesville, GA}
		\item Vivamus PostgreSQL fermentum semper porta. Nunc diam velit PHP, adipiscing ut tristique vitae
		\item Maecenas convallis ullamcorper ultricies stylesheets.
		\item Quisque mi metus, unit tests CSS ornare sit amet fermentum et, tincidunt et orci.
		\item Curabitur venenatis pulvinar tellus gravida ornare. Sed et erat faucibus nunc euismod ultricies ut id
	\end{rSubsection}

\end{rSection}

%----------------------------------------------------------------------------------------
%	TECHNICAL STRENGTHS SECTION
%----------------------------------------------------------------------------------------

\begin{rSection}{Technical Strengths}

	\begin{tabular}{@{} >{\bfseries}l @{\hspace{6ex}} l @{}}
		Computer Languages & Prolog, Haskell, AWK, Erlang, Scheme, ML \\
		Protocols \& APIs & XML, JSON, SOAP, REST \\
		Databases & MySQL, PostgreSQL, Microsoft SQL \\
		Tools & SVN, Vim, Emacs
	\end{tabular}

\end{rSection}

%----------------------------------------------------------------------------------------
%	EXAMPLE SECTION
%----------------------------------------------------------------------------------------

%\begin{rSection}{Section Name}

	%Section content\ldots

%\end{rSection}

%----------------------------------------------------------------------------------------

\end{document}
