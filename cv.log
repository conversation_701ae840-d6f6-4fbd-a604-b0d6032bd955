This is pdfTeX, Version 3.141592653-2.6-1.40.27 (TeX Live 2025) (preloaded format=pdflatex 2025.3.29)  5 JUN 2025 18:38
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**c:/Users/<USER>/CV/cv.tex
(c:/Users/<USER>/CV/cv.tex
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
(./resume.cls
Document Class: resume 2022/12/17 v3.0 Resume class
(c:/texlive/2025/texmf-dist/tex/latex/base/article.cls
Document Class: article 2024/06/29 v1.4n Standard LaTeX document class
(c:/texlive/2025/texmf-dist/tex/latex/base/size11.clo
File: size11.clo 2024/06/29 v1.4n Standard LaTeX file (size option)
)
\c@part=\count196
\c@section=\count197
\c@subsection=\count198
\c@subsubsection=\count199
\c@paragraph=\count266
\c@subparagraph=\count267
\c@figure=\count268
\c@table=\count269
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\bibindent=\dimen141
) (c:/texlive/2025/texmf-dist/tex/latex/parskip/parskip.sty
Package: parskip 2021-03-14 v2.0h non-zero parskip adjustments
 (c:/texlive/2025/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
 (c:/texlive/2025/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (c:/texlive/2025/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (c:/texlive/2025/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)) (c:/texlive/2025/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2025/02/11 v2.5l e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count270
)) (c:/texlive/2025/texmf-dist/tex/latex/tools/array.sty
Package: array 2024/10/17 v2.6g Tabular extension package (FMi)
\col@sep=\dimen142
\ar@mcellbox=\box52
\extrarowheight=\dimen143
\NC@list=\toks18
\extratabsurround=\skip51
\backup@length=\skip52
\ar@cellbox=\box53
) (c:/texlive/2025/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2024/03/16 v1.1e Standard LaTeX ifthen package (DPC)
) (c:/texlive/2025/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (c:/texlive/2025/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2024/08/06 v1.4g Standard LaTeX Graphics (DPC,SPQR)
 (c:/texlive/2025/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2023/12/02 v1.11 sin cos tan (DPC)
) (c:/texlive/2025/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 106.
 (c:/texlive/2025/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2024/04/13 v1.2c Graphics/color driver for pdftex
))
\Gin@req@height=\dimen144
\Gin@req@width=\dimen145
) (c:/texlive/2025/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry
 (c:/texlive/2025/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
 (c:/texlive/2025/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2024/12/12 v1.0g TeX engine tests
))
\Gm@cnth=\count271
\Gm@cntv=\count272
\c@Gm@tempcnt=\count273
\Gm@bindingoffset=\dimen146
\Gm@wd@mp=\dimen147
\Gm@odd@mp=\dimen148
\Gm@even@mp=\dimen149
\Gm@layoutwidth=\dimen150
\Gm@layoutheight=\dimen151
\Gm@layouthoffset=\dimen152
\Gm@layoutvoffset=\dimen153
\Gm@dimlist=\toks19
)) (c:/texlive/2025/texmf-dist/tex/latex/ebgaramond/ebgaramond.sty
Package: ebgaramond 2024/04/23 (Bob Tennent and autoinst) Style file for EB Garamond fonts.
 (c:/texlive/2025/texmf-dist/tex/generic/iftex/ifxetex.sty
Package: ifxetex 2019/10/25 v0.7 ifxetex legacy package. Use iftex instead.
) (c:/texlive/2025/texmf-dist/tex/generic/iftex/ifluatex.sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.
) (c:/texlive/2025/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (c:/texlive/2025/texmf-dist/tex/generic/xkeyval/xkeyval.tex (c:/texlive/2025/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks20
\XKV@tempa@toks=\toks21
)
\XKV@depth=\count274
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (c:/texlive/2025/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2024/04/24 v2.1b Standard LaTeX package
) (c:/texlive/2025/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
) (c:/texlive/2025/texmf-dist/tex/latex/fontaxes/fontaxes.sty
Package: fontaxes 2020/07/21 v1.0e Font selection axes
LaTeX Info: Redefining \upshape on input line 29.
LaTeX Info: Redefining \itshape on input line 31.
LaTeX Info: Redefining \slshape on input line 33.
LaTeX Info: Redefining \swshape on input line 35.
LaTeX Info: Redefining \scshape on input line 37.
LaTeX Info: Redefining \sscshape on input line 39.
LaTeX Info: Redefining \ulcshape on input line 41.
LaTeX Info: Redefining \textsw on input line 47.
LaTeX Info: Redefining \textssc on input line 48.
LaTeX Info: Redefining \textulc on input line 49.
)
LaTeX Info: Redefining \oldstylenums on input line 163.
LaTeX Info: Redefining \textsw on input line 173.
) (c:/texlive/2025/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-11-05 v7.01l Hypertext links for LaTeX
 (c:/texlive/2025/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (c:/texlive/2025/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (c:/texlive/2025/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (c:/texlive/2025/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
)) (c:/texlive/2025/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (c:/texlive/2025/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (c:/texlive/2025/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (c:/texlive/2025/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count275
) (c:/texlive/2025/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\@linkdim=\dimen154
\Hy@linkcounter=\count276
\Hy@pagecounter=\count277
 (c:/texlive/2025/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-11-05 v7.01l Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
) (c:/texlive/2025/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count278
 (c:/texlive/2025/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-11-05 v7.01l Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4157.
Package hyperref Info: Link nesting OFF on input line 4162.
Package hyperref Info: Hyper index ON on input line 4165.
Package hyperref Info: Plain pages OFF on input line 4172.
Package hyperref Info: Backreferencing OFF on input line 4177.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4424.
\c@Hy@tempcnt=\count279
 (c:/texlive/2025/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4763.
\XeTeXLinkMargin=\dimen155
 (c:/texlive/2025/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (c:/texlive/2025/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count280
\Field@Width=\dimen156
\Fld@charsize=\dimen157
Package hyperref Info: Hyper figures OFF on input line 6042.
Package hyperref Info: Link nesting OFF on input line 6047.
Package hyperref Info: Hyper index ON on input line 6050.
Package hyperref Info: backreferencing OFF on input line 6057.
Package hyperref Info: Link coloring OFF on input line 6062.
Package hyperref Info: Link coloring with OCG OFF on input line 6067.
Package hyperref Info: PDF/A mode OFF on input line 6072.
 (c:/texlive/2025/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count281
\c@Item=\count282
\c@Hfootnote=\count283
)
Package hyperref Info: Driver (autodetected): hpdftex.
 (c:/texlive/2025/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-11-05 v7.01l Hyperref driver for pdfTeX
 (c:/texlive/2025/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
)
\Fld@listcount=\count284
\c@bookmark@seq@number=\count285
 (c:/texlive/2025/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (c:/texlive/2025/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip53
) (c:/texlive/2025/texmf-dist/tex/latex/fontawesome/fontawesome.sty
Package: fontawesome 2016/05/15 v4.6.3.1 font awesome icons
 (c:/texlive/2025/texmf-dist/tex/latex/fontawesome/fontawesomesymbols-generic.tex) (c:/texlive/2025/texmf-dist/tex/latex/fontawesome/fontawesomesymbols-pdftex.tex)) (c:/texlive/2025/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2024/09/29 v3.02 LaTeX color extensions (UK)
 (c:/texlive/2025/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.
 (c:/texlive/2025/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1349.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1365.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1367.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1371.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1372.
)
LaTeX Font Info:    Trying to load font information for OT1+EBGaramond-OsF on input line 19.
 (c:/texlive/2025/texmf-dist/tex/latex/ebgaramond/OT1EBGaramond-OsF.fd
File: OT1EBGaramond-OsF.fd 2023/03/19 (autoinst) Font definitions for OT1/EBGaramond-OsF.
)
LaTeX Font Info:    Font shape `OT1/EBGaramond-OsF/m/n' will be
(Font)              scaled to size 10.95pt on input line 19.
 (c:/texlive/2025/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-05-08 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count286
\l__pdf_internal_box=\box54
) (./cv.aux)
\openout1 = `cv.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 19.
LaTeX Font Info:    ... okay on input line 19.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 19.
LaTeX Font Info:    ... okay on input line 19.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 19.
LaTeX Font Info:    ... okay on input line 19.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 19.
LaTeX Font Info:    ... okay on input line 19.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 19.
LaTeX Font Info:    ... okay on input line 19.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 19.
LaTeX Font Info:    ... okay on input line 19.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 19.
LaTeX Font Info:    ... okay on input line 19.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 19.
LaTeX Font Info:    ... okay on input line 19.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 19.
LaTeX Font Info:    ... okay on input line 19.
 (c:/texlive/2025/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count287
\scratchdimen=\dimen158
\scratchbox=\box55
\nofMPsegments=\count288
\nofMParguments=\count289
\everyMPshowfont=\toks22
\MPscratchCnt=\count290
\MPscratchDim=\dimen159
\MPnumerator=\count291
\makeMPintoPDFobject=\count292
\everyMPtoPDFconversion=\toks23
) (c:/texlive/2025/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (c:/texlive/2025/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: <default>
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(54.2025pt, 505.89pt, 54.2025pt)
* v-part:(T,H,B)=(43.36243pt, 708.24513pt, 43.36243pt)
* \paperwidth=614.295pt
* \paperheight=794.96999pt
* \textwidth=505.89pt
* \textheight=708.24513pt
* \oddsidemargin=-18.06749pt
* \evensidemargin=-18.06749pt
* \topmargin=-65.90756pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=11.0pt
* \footskip=30.0pt
* \marginparwidth=59.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

\c@mv@tabular=\count293
\c@mv@boldtabular=\count294
Package hyperref Info: Link coloring OFF on input line 19.
(./cv.out) (./cv.out)
\@outlinefile=\write3
\openout3 = `cv.out'.

LaTeX Font Info:    Font shape `OT1/EBGaramond-OsF/m/n' will be
(Font)              scaled to size 20.74pt on input line 19.
LaTeX Font Info:    Font shape `OT1/EBGaramond-OsF/b/n' will be
(Font)              scaled to size 20.74pt on input line 19.

Overfull \hbox (30.0pt too wide) in paragraph at lines 19--19
[] 
 []

LaTeX Font Info:    Font shape `OT1/EBGaramond-OsF/b/n' will be
(Font)              scaled to size 10.95pt on input line 21.
LaTeX Font Info:    Font shape `OT1/EBGaramond-OsF/m/it' will be
(Font)              scaled to size 10.95pt on input line 23.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <10.95> on input line 39.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <8> on input line 39.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <6> on input line 39.


[1

{c:/texlive/2025/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{c:/texlive/2025/texmf-dist/fonts/enc/dvips/ebgaramond/ebg_qn2lct.enc}{c:/texlive/2025/texmf-dist/fonts/enc/dvips/ebgaramond/ebg_ne3tkd.enc}]
LaTeX Font Info:    Font shape `OT1/EBGaramond-OsF/m/n' will be
(Font)              scaled to size 12.0pt on input line 63.
LaTeX Font Info:    Font shape `OT1/EBGaramond-OsF/b/n' will be
(Font)              scaled to size 12.0pt on input line 63.

Overfull \hbox (13.57394pt too wide) in paragraph at lines 87--89
\OT1/EBGaramond-OsF/m/n/10.95 Good fa-mil-iar-ity with \OT1/cmtt/m/n/10.95 Solidity \OT1/EBGaramond-OsF/m/n/10.95 and rel-e-vant blockchain de-vel-op-ment tools such as \OT1/cmtt/m/n/10.95 Hardhat\OT1/EBGaramond-OsF/m/n/10.95 , \OT1/cmtt/m/n/10.95 ethers.js\OT1/EBGaramond-OsF/m/n/10.95 , and \OT1/cmtt/m/n/10.95 GraphQL 
 []



[2] (./cv.aux)
 ***********
LaTeX2e <2024-11-01> patch level 2
L3 programming layer <2025-01-18>
 ***********
Package rerunfilecheck Info: File `cv.out' has not changed.
(rerunfilecheck)             Checksum: D41D8CD98F00B204E9800998ECF8427E;0.
 ) 
Here is how much of TeX's memory you used:
 11991 strings out of 473190
 186379 string characters out of 5719979
 599345 words of memory out of 5000000
 35103 multiletter control sequences out of 15000+600000
 571577 words of font info for 53 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 72i,5n,79p,517b,532s stack positions out of 10000i,1000n,20000p,200000b,200000s
<c:/texlive/2025/texmf-dist/fonts/type1/public/ebgaramond/EBGaramond-Bold.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/ebgaramond/EBGaramond-Italic.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/ebgaramond/EBGaramond-Regular.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb><c:/texlive/2025/texmf-dist/fonts/type1/public/amsfonts/cm/cmtt10.pfb>
Output written on cv.pdf (2 pages, 94742 bytes).
PDF statistics:
 46 PDF objects out of 1000 (max. 8388607)
 31 compressed objects within 1 object stream
 3 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

