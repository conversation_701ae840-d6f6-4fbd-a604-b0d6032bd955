\documentclass[11pt]{resume}

\usepackage{e<PERSON><PERSON><PERSON>}
\usepackage{hyperref}
\usepackage{fontawesome}
\usepackage{xcolor}

% Define colors
\definecolor{sectioncolor}{RGB}{51,51,51}
\definecolor{subsectioncolor}{RGB}{70,70,70}

% Name and contact information
\name{<PERSON><PERSON>}

\address{Ph.D. Student in Psychiatric Genetics}
\address{Hong Kong, China | London, UK}
\address{\href{mailto:<EMAIL>}{<EMAIL>} | \href{mailto:<EMAIL>}{<EMAIL>}}

\begin{document}

\begin{rSection}{Education}

\textbf{The University of Hong Kong \& King's College London} \hfill \textit{2023 -- Present} \\
Ph.D. Student in Statistical Genetics \hfill Hong Kong, China \& London, UK \\
\textbf{Research Focus:} Developing prediction models for antidepressant treatment response and side effects with electronic health records, survey, and genomic data \\
\textbf{Supervisors:} Professor <PERSON> and Professor <PERSON><PERSON><PERSON>

\smallskip

\textbf{King's College London} \hfill \textit{2017 -- 2021} \\
BSc Psychology and Mathematics, First Class Honours (3.9/4.0) \hfill London, UK \\
\textbf{Thesis:} ``Genome Wide Association Studies of Antidepressant Treatment Response and Side Effects in the Genetic Links to Anxiety and Depression (GLAD) Study''

\end{rSection}

\begin{rSection}{Experience}

\begin{rSubsection}{Sham Lab, The University of Hong Kong \& TNG Lab, King's College London}{2023 -- Present}{Ph.D. Student in Statistical Genetics}{Hong Kong, China \& London, UK}
    \item Developing a causal machine learning framework to estimate causal effects from longitudinal Bayesian networks with genetic instruments to decompose side effects of psychiatric medications and predict treatment trajectories
    \item Leveraging large language models by fine-tuning on specialized medical language tasks to extract structured variables from unstructured medical notes
    \item Implemented a comprehensive quality control pipeline for genotyping data optimized for high performance computing environments.
    \item Developed an integrated GWAS pipeline optimized for high performance computing environments to unify multiple statistical genetics analysis tools
    \item Improved the `ldsc' library's standard error estimation for complex trait heritability analysis
\end{rSubsection}

\begin{rSubsection}{Freelance Blockchain Developer}{2023-2024}{}{China}
    \item Developed smart contracts for an NFT project with complex mechanisms including randomness with on-chain oracles and dynamic NFTs
    \item Worked with a front-end developer to ensure seamless on-chain data retrieval and storage with GraphQL
    \item Winner of Base Onchain Summer Buildathon 2024 
\end{rSubsection}

\begin{rSubsection}{TNG Lab, King's College London}{2019 -- 2022}{Research Assistant}{London, UK}
    \item Developed and maintained an R package for automated fetching of large-scale online survey data and processing in the GLAD Study 
    \item Conducted a research project analyzing correlates of antidepressant response and side effects in the GLAD Study dataset
\end{rSubsection}

\begin{rSubsection}{Computational Medicine Group, King's College London}{2019}{Summer Research Assistant}{London, UK}
    \item Performed statistical analyses investigating the association between gut microbiome species, metabolic pathways, and human diseases
\end{rSubsection}

\newpage
\vspace{0.5em}
{\large\textbf{Teaching Experience}}\vspace{0.3em}
\hrule
\vspace{0.3em}
\begin{rSubsection}{Instructor}{2024}{Mendelian Randomization}{China}
    \item Led specialized online workshops on Mendelian Randomization for medical researchers, covering causal inference methods using genetic instruments in epidemiological studies
\end{rSubsection}

\begin{rSubsection}{Graduate Teaching Assistant}{2023}{Statistical Methods and R Programming}{London, UK}
    \item Provided hands-on instruction in statistical methods and R programming for graduate students.
\end{rSubsection}

\begin{rSubsection}{Instructor}{2021}{Programming and Statistics in R}{London, UK}
    \item Designed and conducted weekly workshops on advanced R programming and statistical methods for incoming postgraduate researchers.
\end{rSubsection}

\end{rSection}

\begin{rSection}{Computational Skills}

\textbf{Programming Languages:} \\
\texttt{Python}, \texttt{R}, \texttt{C++}, \texttt{JavaScript}, \texttt{MATLAB} and \texttt{SQL}

\smallskip

\textbf{Smart Contract Development:} \\
Good familiarity with \texttt{Solidity} and relevant blockchain development tools such as \texttt{Hardhat}, \texttt{ethers.js}, and \texttt{GraphQL}

\end{rSection}

\end{document}
